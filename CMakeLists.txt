cmake_minimum_required(VERSION 3.14)
project(multipov)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Add compile options
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra")

# Include directories
include_directories(src)

# Source files
file(GLOB_RECURSE SOURCES "src/*.cpp")
list(FILTER SOURCES EXCLUDE REGEX ".*main\\.cpp$")

# Create library from source files (excluding main.cpp)
if(SOURCES)
    add_library(${PROJECT_NAME}_lib ${SOURCES})
endif()

# Main executable
add_executable(${PROJECT_NAME} src/main.cpp)
if(SOURCES)
    target_link_libraries(${PROJECT_NAME} ${PROJECT_NAME}_lib)
endif()

# Google Test
include(FetchContent)
FetchContent_Declare(
  googletest
  URL https://github.com/google/googletest/archive/03597a01ee50ed33e9fd7188ec8e5902e4ec0908.zip
)
# For Windows: Prevent overriding the parent project's compiler/linker settings
set(gtest_force_shared_crt ON CACHE BOOL "" FORCE)
FetchContent_MakeAvailable(googletest)

# Test executable
file(GLOB_RECURSE TEST_SOURCES "test/*.cpp")
if(TEST_SOURCES)
    add_executable(${PROJECT_NAME}_tests ${TEST_SOURCES})
    target_link_libraries(${PROJECT_NAME}_tests 
        gtest_main
        gmock_main
    )
    if(SOURCES)
        target_link_libraries(${PROJECT_NAME}_tests ${PROJECT_NAME}_lib)
    endif()
    
    # Enable testing
    enable_testing()
    include(GoogleTest)
    gtest_discover_tests(${PROJECT_NAME}_tests)
endif()
