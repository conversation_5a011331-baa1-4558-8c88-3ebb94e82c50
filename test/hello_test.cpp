#include <gtest/gtest.h>
#include "lib/hello.h"

TEST(HelloTest, SayHelloReturnsCorrectGreeting) {
    std::string result = sayHello("World");
    EXPECT_EQ(result, "Hello, World!");
}

TEST(HelloTest, SayHelloWithEmptyString) {
    std::string result = sayHello("");
    EXPECT_EQ(result, "Hello, !");
}

TEST(HelloTest, SayHelloWithCustomName) {
    std::string result = sayHello("Bazel");
    EXPECT_EQ(result, "Hello, Bazel!");
}
