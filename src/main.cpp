#include <iostream>
#include "lib/sliding_window_buffer.h"

int main() {
    std::cout << "Welcome to multipov!" << std::endl;

    // Demonstrate SlidingWindowBuffer
    std::cout << "\nSlidingWindowBuffer Demo:" << std::endl;

    SlidingWindowBuffer<int> buffer(3);

    // Add some numbers
    for (int i = 1; i <= 5; ++i) {
        buffer.push(i * 10);
        std::cout << "Added " << i * 10 << ", buffer contents: ";
        for (size_t j = 0; j < buffer.size(); ++j) {
            std::cout << buffer[j] << " ";
        }
        std::cout << "(size: " << buffer.size() << ")" << std::endl;
    }

    std::cout << "\nFinal buffer state:" << std::endl;
    std::cout << "Front: " << buffer.front() << std::endl;
    std::cout << "Back: " << buffer.back() << std::endl;
    std::cout << "Full: " << (buffer.full() ? "yes" : "no") << std::endl;

    return 0;
}
