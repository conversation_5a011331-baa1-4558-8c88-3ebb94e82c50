#pragma once
#include <vector>
#include <stdexcept>

template<typename T>
class SlidingWindowBuffer {
private:
    std::vector<T> buffer_;
    size_t capacity_;
    size_t head_;
    size_t size_;

public:
    // Constructor
    explicit SlidingWindowBuffer(size_t capacity) 
        : buffer_(capacity), capacity_(capacity), head_(0), size_(0) {
        if (capacity == 0) {
            throw std::invalid_argument("Capacity must be greater than 0");
        }
    }

    // Add an element to the buffer
    void push(const T& item) {
        if (size_ < capacity_) {
            buffer_[size_] = item;
            ++size_;
        } else {
            buffer_[head_] = item;
            head_ = (head_ + 1) % capacity_;
        }
    }

    // Get element at index (0 is oldest, size()-1 is newest)
    const T& at(size_t index) const {
        if (index >= size_) {
            throw std::out_of_range("Index out of range");
        }
        
        if (size_ < capacity_) {
            return buffer_[index];
        } else {
            size_t actual_index = (head_ + index) % capacity_;
            return buffer_[actual_index];
        }
    }

    // Get element at index (non-const version)
    T& at(size_t index) {
        if (index >= size_) {
            throw std::out_of_range("Index out of range");
        }
        
        if (size_ < capacity_) {
            return buffer_[index];
        } else {
            size_t actual_index = (head_ + index) % capacity_;
            return buffer_[actual_index];
        }
    }

    // Operator[] for convenient access
    const T& operator[](size_t index) const {
        return at(index);
    }

    T& operator[](size_t index) {
        return at(index);
    }

    // Get the newest element
    const T& back() const {
        if (empty()) {
            throw std::runtime_error("Buffer is empty");
        }
        return at(size_ - 1);
    }

    // Get the oldest element
    const T& front() const {
        if (empty()) {
            throw std::runtime_error("Buffer is empty");
        }
        return at(0);
    }

    // Get current size
    size_t size() const {
        return size_;
    }

    // Get capacity
    size_t capacity() const {
        return capacity_;
    }

    // Check if buffer is empty
    bool empty() const {
        return size_ == 0;
    }

    // Check if buffer is full
    bool full() const {
        return size_ == capacity_;
    }

    // Clear the buffer
    void clear() {
        size_ = 0;
        head_ = 0;
    }

    // Get all elements in order (oldest to newest)
    std::vector<T> to_vector() const {
        std::vector<T> result;
        result.reserve(size_);
        for (size_t i = 0; i < size_; ++i) {
            result.push_back(at(i));
        }
        return result;
    }
};
