# Multipov

A C++ project built with <PERSON><PERSON> and Google Test.

## Project Structure

```
multipov/
├── WORKSPACE          # Bazel workspace configuration
├── BUILD.bazel        # Main build configuration
├── .bazelrc          # Bazel configuration options
├── src/              # Source code
│   ├── main.cpp      # Main application entry point
│   ├── hello.h       # Header file
│   └── hello.cpp     # Implementation file
└── test/             # Test code
    ├── BUILD.bazel   # Test build configuration
    └── hello_test.cpp # Google Test unit tests
```

## Building

To build the main application:
```bash
bazel build //:multipov
```

To run the application:
```bash
bazel run //:multipov
```

## Testing

To run all tests:
```bash
bazel test //test:all
```

To run a specific test:
```bash
bazel test //test:hello_test
```

To run tests with verbose output:
```bash
bazel test //test:all --test_output=all
```

## Requirements

- Bazel (latest version recommended)
- C++17 compatible compiler
